"""
User-Selected Visualization Agent

This module implements a VisualizationAgent that inherits from UserSelectedAgent,
providing user-controlled collaboration capabilities while maintaining the
visualization agent's core functionality of creating charts and visual representations.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .user_selected_agent import UserSelectedAgent, CollaborationNeed
from ..nodes.unified_persona_node import UnifiedPersonaNode

logger = logging.getLogger(__name__)


class UserSelectedVisualizationAgent(UserSelectedAgent):
    """
    Visualization agent that can be directly selected by users.
    
    This agent provides:
    - Data visualization and charting
    - Interactive dashboard creation
    - Infographic and visual content design
    - User-controlled collaboration with specialists
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the user-selected visualization agent.
        
        Args:
            config: Optional configuration dictionary
        """
        # Define visualization capabilities
        capabilities = [
            "data_visualization",
            "chart_creation",
            "dashboard_design",
            "infographic_creation",
            "interactive_visualization",
            "visual_storytelling",
            "chart_optimization",
            "visual_analytics"
        ]
        
        super().__init__(
            agent_id="user_selected_visualization",
            capabilities=capabilities,
            config=config
        )
        
        # Initialize the underlying unified persona node for visualization functionality
        visualization_config = {
            "persona_id": "visualization",
            "agent_type": "visualization",
            "name": "Visualization Specialist",
            "description": "Expert in creating compelling data visualizations and interactive dashboards",
            "strategy_id": "visualization",
            "capabilities": capabilities
        }
        
        # Merge with provided config
        if config:
            visualization_config.update(config)
        
        self.unified_node = UnifiedPersonaNode(
            persona_config=visualization_config,
            business_profile=config.get('business_profile') if config else None
        )
        
        # Visualization-specific configuration
        self.chart_types = config.get('chart_types', [
            'bar', 'line', 'scatter', 'pie', 'histogram', 'box', 'heatmap', 
            'treemap', 'sunburst', 'sankey', 'waterfall'
        ]) if config else [
            'bar', 'line', 'scatter', 'pie', 'histogram', 'box', 'heatmap', 
            'treemap', 'sunburst', 'sankey', 'waterfall'
        ]
        
        self.visualization_libraries = config.get('visualization_libraries', [
            'plotly', 'matplotlib', 'seaborn', 'd3js', 'bokeh', 'altair'
        ]) if config else [
            'plotly', 'matplotlib', 'seaborn', 'd3js', 'bokeh', 'altair'
        ]
        
        self.output_formats = config.get('output_formats', [
            'png', 'svg', 'pdf', 'html', 'interactive'
        ]) if config else [
            'png', 'svg', 'pdf', 'html', 'interactive'
        ]
        
        logger.info("UserSelectedVisualizationAgent initialized")

    async def handle_with_own_capabilities(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handle the user message using the visualization agent's own capabilities.
        
        This method provides:
        - Data visualization and chart creation
        - Interactive dashboard design
        - Visual storytelling and infographics
        - Chart optimization and styling
        
        Args:
            message: The user's message
            context: Additional context information
            
        Returns:
            Dict containing the visualization agent's response and metadata
        """
        try:
            logger.debug("Visualization agent handling message with own capabilities")
            
            # Prepare state for unified node processing
            state = {
                "messages": [{"role": "user", "content": message}],
                "user_id": context.get("user_id") if context else None,
                "conversation_id": context.get("conversation_id") if context else None,
                "business_profile": context.get("business_profile") if context else None,
                "metadata": context.get("metadata", {}) if context else {}
            }
            
            # Add visualization-specific context
            state["visualization_context"] = {
                "chart_types": self.chart_types,
                "visualization_libraries": self.visualization_libraries,
                "output_formats": self.output_formats
            }
            
            # Process with the unified persona node
            processed_state = await self.unified_node._process_message(state)
            
            # Extract response from processed state
            response_message = ""
            if "messages" in processed_state and processed_state["messages"]:
                # Get the last assistant message
                for msg in reversed(processed_state["messages"]):
                    if msg.get("role") == "assistant":
                        response_message = msg.get("content", "")
                        break
            
            if not response_message:
                response_message = "I'm your Visualization Specialist! I can create compelling charts, interactive dashboards, and visual stories from your data. What would you like to visualize?"
            
            # Add visualization-specific enhancements
            enhanced_response = await self._enhance_visualization_response(message, response_message, context)
            
            return {
                "message": enhanced_response,
                "metadata": {
                    "agent_type": "visualization",
                    "capabilities_used": self._identify_used_capabilities(message),
                    "processing_method": "unified_persona_node",
                    "visualization_type": self._identify_visualization_type(message),
                    "timestamp": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error in visualization own capabilities handling: {e}")
            return {
                "message": "I'm your Visualization Specialist! I excel at creating beautiful and insightful charts, interactive dashboards, and compelling visual stories. Share your data or describe what you'd like to visualize!",
                "metadata": {
                    "error": str(e),
                    "fallback_response": True,
                    "timestamp": datetime.now().isoformat()
                }
            }

    async def assess_collaboration_needs(self, message: str, context: Optional[Dict[str, Any]] = None) -> List[CollaborationNeed]:
        """
        Assess whether collaboration with other agents is needed.
        
        The visualization agent determines if it needs help from specialists like
        data analysts for data preparation, or marketing experts for branded visuals.
        
        Args:
            message: The user's message
            context: Additional context information
            
        Returns:
            List of CollaborationNeed objects
        """
        try:
            collaboration_needs = []
            message_lower = message.lower()
            
            # Check for data analysis needs before visualization
            data_prep_keywords = [
                "clean data", "prepare data", "data preprocessing", "data analysis",
                "statistical analysis", "data insights", "analyze before visualizing"
            ]
            if any(keyword in message_lower for keyword in data_prep_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Data Analysis Specialist",
                    task_description="analyze and prepare data for visualization",
                    required_capability="data_analysis",
                    priority="high",
                    context={"analysis_type": "data_preparation", "user_message": message}
                ))
            
            # Check for marketing visualization needs
            marketing_viz_keywords = [
                "marketing dashboard", "campaign visualization", "brand colors",
                "marketing infographic", "promotional chart", "branded visualization"
            ]
            if any(keyword in message_lower for keyword in marketing_viz_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Marketing Expert",
                    task_description="provide marketing context and branding for visualizations",
                    required_capability="marketing_strategy",
                    priority="normal",
                    context={"viz_type": "marketing", "user_message": message}
                ))
            
            # Check for business intelligence dashboard needs
            bi_dashboard_keywords = [
                "executive dashboard", "business intelligence", "kpi dashboard",
                "strategic visualization", "performance dashboard", "business metrics"
            ]
            if any(keyword in message_lower for keyword in bi_dashboard_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Business Intelligence Specialist",
                    task_description="provide business context and KPI guidance for dashboards",
                    required_capability="business_intelligence",
                    priority="normal",
                    context={"viz_type": "business_intelligence", "user_message": message}
                ))
            
            # Check for content classification for visual organization
            content_viz_keywords = [
                "content visualization", "text visualization", "document visualization",
                "topic visualization", "sentiment visualization", "category charts"
            ]
            if any(keyword in message_lower for keyword in content_viz_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Classification Specialist",
                    task_description="classify and organize content for visualization",
                    required_capability="text_classification",
                    priority="normal",
                    context={"viz_type": "content", "user_message": message}
                ))
            
            logger.debug(f"Visualization agent identified {len(collaboration_needs)} collaboration needs")
            return collaboration_needs
            
        except Exception as e:
            logger.error(f"Error assessing collaboration needs: {e}")
            return []

    async def _enhance_visualization_response(self, original_message: str, base_response: str, 
                                            context: Optional[Dict[str, Any]] = None) -> str:
        """
        Enhance the visualization response with additional helpful information.
        
        Args:
            original_message: The user's original message
            base_response: The base response from the unified node
            context: Additional context
            
        Returns:
            Enhanced response string
        """
        try:
            enhanced_response = base_response
            
            # Add chart type recommendations
            if self._is_chart_request(original_message):
                chart_suggestions = self._suggest_chart_types(original_message)
                if chart_suggestions:
                    enhanced_response += f"\n\n📊 **Chart Recommendations**: Based on your request, I suggest: {', '.join(chart_suggestions)}"
            
            # Add interactivity options
            if self._is_interactive_request(original_message):
                enhanced_response += f"\n\n🖱️ **Interactive Features**: I can create interactive visualizations with hover details, filtering, zooming, and drill-down capabilities."
            
            # Add output format options
            if self._is_export_request(original_message):
                enhanced_response += f"\n\n💾 **Export Options**: I can provide visualizations in these formats: {', '.join(self.output_formats)}"
            
            # Add dashboard guidance
            if self._is_dashboard_request(original_message):
                enhanced_response += "\n\n📈 **Dashboard Design**: I can create comprehensive dashboards with multiple charts, filters, and real-time data connections."
            
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Error enhancing visualization response: {e}")
            return base_response

    def _identify_used_capabilities(self, message: str) -> List[str]:
        """Identify which capabilities were likely used based on the message."""
        used_capabilities = ["data_visualization"]  # Always include base capability
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["chart", "graph", "plot"]):
            used_capabilities.append("chart_creation")
        
        if any(word in message_lower for word in ["dashboard", "panel", "overview"]):
            used_capabilities.append("dashboard_design")
        
        if any(word in message_lower for word in ["interactive", "clickable", "hover"]):
            used_capabilities.append("interactive_visualization")
        
        if any(word in message_lower for word in ["infographic", "visual story", "narrative"]):
            used_capabilities.append("visual_storytelling")
        
        if any(word in message_lower for word in ["optimize", "improve", "enhance"]):
            used_capabilities.append("chart_optimization")
        
        return used_capabilities

    def _identify_visualization_type(self, message: str) -> str:
        """Identify the main visualization type requested."""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["dashboard", "panel"]):
            return "dashboard"
        elif any(word in message_lower for word in ["chart", "graph"]):
            return "chart"
        elif any(word in message_lower for word in ["infographic", "visual story"]):
            return "infographic"
        elif any(word in message_lower for word in ["interactive", "dynamic"]):
            return "interactive"
        else:
            return "general"

    def _is_chart_request(self, message: str) -> bool:
        """Check if the message is requesting a specific chart."""
        chart_keywords = ["chart", "graph", "plot", "visualization", "visualize"]
        return any(keyword in message.lower() for keyword in chart_keywords)

    def _is_interactive_request(self, message: str) -> bool:
        """Check if the message is requesting interactive features."""
        interactive_keywords = ["interactive", "clickable", "hover", "filter", "drill", "dynamic"]
        return any(keyword in message.lower() for keyword in interactive_keywords)

    def _is_export_request(self, message: str) -> bool:
        """Check if the message mentions export or output formats."""
        export_keywords = ["export", "save", "download", "format", "output", "file"]
        return any(keyword in message.lower() for keyword in export_keywords)

    def _is_dashboard_request(self, message: str) -> bool:
        """Check if the message is requesting a dashboard."""
        dashboard_keywords = ["dashboard", "panel", "overview", "summary", "multiple charts"]
        return any(keyword in message.lower() for keyword in dashboard_keywords)

    def _suggest_chart_types(self, message: str) -> List[str]:
        """Suggest appropriate chart types based on the message."""
        message_lower = message.lower()
        suggestions = []
        
        if any(word in message_lower for word in ["trend", "time", "over time"]):
            suggestions.extend(["line", "area"])
        
        if any(word in message_lower for word in ["compare", "comparison", "categories"]):
            suggestions.extend(["bar", "column"])
        
        if any(word in message_lower for word in ["distribution", "frequency"]):
            suggestions.extend(["histogram", "box"])
        
        if any(word in message_lower for word in ["relationship", "correlation"]):
            suggestions.extend(["scatter", "bubble"])
        
        if any(word in message_lower for word in ["proportion", "percentage", "parts"]):
            suggestions.extend(["pie", "donut"])
        
        if any(word in message_lower for word in ["hierarchy", "tree", "nested"]):
            suggestions.extend(["treemap", "sunburst"])
        
        return suggestions[:3]  # Return top 3 suggestions

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this visualization agent."""
        base_info = super().get_agent_info()
        base_info.update({
            "agent_type": "visualization",
            "specialization": "data_visualization_and_dashboards",
            "chart_types": self.chart_types,
            "visualization_libraries": self.visualization_libraries,
            "output_formats": self.output_formats,
            "supports_interactive_viz": True,
            "supports_dashboards": True,
            "supports_infographics": True
        })
        return base_info
