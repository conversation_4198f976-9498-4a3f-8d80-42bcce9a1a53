"""
User-Selected Agent Base Class

This module provides the base class for agents that are directly selected by users,
implementing the user-controlled collaboration pattern where agents use the network
as tools rather than being automatically routed.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from dataclasses import dataclass

from ..permissions.user_permission_manager import UserPermissionManager
from ..tools.network_collaboration_tools import NetworkCollaborationTools
from ..states.network_state import NetworkDatageniusState


logger = logging.getLogger(__name__)


@dataclass
class CollaborationNeed:
    """Represents a need for collaboration with another agent."""
    specialist_type: str
    task_description: str
    required_capability: str
    priority: str = "normal"  # low, normal, high
    context: Optional[Dict[str, Any]] = None


@dataclass
class CollaborationResult:
    """Result from agent collaboration."""
    specialist_id: str
    specialist_type: str
    result: str
    metadata: Optional[Dict[str, Any]] = None
    success: bool = True


class UserSelectedAgent(ABC):
    """
    Base class for agents that are directly selected by users.
    
    This class implements the user-controlled collaboration pattern where:
    1. Users explicitly select which agent they want to interact with
    2. The selected agent handles all user communication directly
    3. The agent can request user permission to collaborate with other agents
    4. The network serves as a tool for the selected agent, not for routing
    5. The selected agent remains the primary interface throughout the conversation
    """

    def __init__(self, agent_id: str, capabilities: List[str], config: Optional[Dict[str, Any]] = None):
        """
        Initialize the user-selected agent.
        
        Args:
            agent_id: Unique identifier for this agent
            capabilities: List of capabilities this agent provides
            config: Optional configuration dictionary
        """
        self.agent_id = agent_id
        self.capabilities = capabilities
        self.config = config or {}
        
        # Initialize collaboration tools
        self.network_tools = NetworkCollaborationTools(agent_id=agent_id)
        self.permission_manager = UserPermissionManager()
        
        # Agent state
        self.is_active = True
        self.current_conversations: Dict[str, Dict[str, Any]] = {}
        self.collaboration_history: List[Dict[str, Any]] = []
        
        # Configuration
        self.max_collaboration_timeout = self.config.get('collaboration_timeout', 30)
        self.max_concurrent_collaborations = self.config.get('max_concurrent_collaborations', 3)
        self.auto_permission_for_capabilities = self.config.get('auto_permission_capabilities', [])
        
        logger.info(f"UserSelectedAgent {agent_id} initialized with capabilities: {capabilities}")

    async def process_user_message(self, message: str, user_id: str, conversation_id: str, 
                                 context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Main entry point for processing user messages.
        
        This method implements the core user-controlled collaboration pattern:
        1. Always try to handle the message with the agent's own capabilities first
        2. Assess if collaboration with other agents is needed
        3. Request user permission for each needed collaboration
        4. Coordinate with approved specialists
        5. Integrate all results into a unified response
        
        Args:
            message: The user's message
            user_id: ID of the user sending the message
            conversation_id: ID of the conversation
            context: Additional context information
            
        Returns:
            Dict containing the response and metadata
        """
        try:
            logger.info(f"Agent {self.agent_id} processing message from user {user_id}")
            
            # Initialize conversation context if needed
            if conversation_id not in self.current_conversations:
                self.current_conversations[conversation_id] = {
                    'user_id': user_id,
                    'started_at': datetime.now().isoformat(),
                    'message_count': 0,
                    'collaborations': []
                }
            
            # Update conversation state
            conv_context = self.current_conversations[conversation_id]
            conv_context['message_count'] += 1
            conv_context['last_message_at'] = datetime.now().isoformat()
            
            # Step 1: Handle with own capabilities first
            logger.debug(f"Agent {self.agent_id} handling message with own capabilities")
            initial_response = await self.handle_with_own_capabilities(message, context)
            
            # Step 2: Assess collaboration needs
            logger.debug(f"Agent {self.agent_id} assessing collaboration needs")
            collaboration_needs = await self.assess_collaboration_needs(message, context)
            
            # Step 3: Handle collaborations if needed
            collaboration_results = []
            if collaboration_needs:
                logger.info(f"Agent {self.agent_id} identified {len(collaboration_needs)} collaboration needs")
                collaboration_results = await self._handle_collaborations(
                    collaboration_needs, user_id, conversation_id, message, context
                )
            
            # Step 4: Integrate all results
            final_response = await self.integrate_responses(
                initial_response, collaboration_results, message, context
            )
            
            # Step 5: Update conversation history
            conv_context['collaborations'].extend([
                {
                    'specialist_type': result.specialist_type,
                    'success': result.success,
                    'timestamp': datetime.now().isoformat()
                }
                for result in collaboration_results
            ])
            
            # Add agent metadata
            final_response['metadata'] = final_response.get('metadata', {})
            final_response['metadata'].update({
                'agent_id': self.agent_id,
                'capabilities_used': self.capabilities,
                'collaborations_count': len(collaboration_results),
                'conversation_id': conversation_id,
                'processing_timestamp': datetime.now().isoformat()
            })
            
            logger.info(f"Agent {self.agent_id} completed message processing")
            return final_response
            
        except Exception as e:
            logger.error(f"Error in agent {self.agent_id} processing message: {e}")
            return {
                'message': f"I encountered an error while processing your request. Please try again.",
                'metadata': {
                    'error': str(e),
                    'agent_id': self.agent_id,
                    'error_timestamp': datetime.now().isoformat()
                }
            }

    @abstractmethod
    async def handle_with_own_capabilities(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handle the user message using only this agent's own capabilities.
        
        This method should implement the agent's core functionality without
        relying on other agents. It should return a complete response that
        could stand alone if no collaboration is needed.
        
        Args:
            message: The user's message
            context: Additional context information
            
        Returns:
            Dict containing the agent's response and any metadata
        """
        pass

    @abstractmethod
    async def assess_collaboration_needs(self, message: str, context: Optional[Dict[str, Any]] = None) -> List[CollaborationNeed]:
        """
        Assess whether collaboration with other agents is needed for this message.
        
        This method should analyze the user's message and determine if the agent
        needs help from specialists to provide a complete response.
        
        Args:
            message: The user's message
            context: Additional context information
            
        Returns:
            List of CollaborationNeed objects describing needed collaborations
        """
        pass

    async def integrate_responses(self, initial_response: Dict[str, Any], 
                                collaboration_results: List[CollaborationResult],
                                original_message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Integrate the agent's initial response with collaboration results.
        
        This method combines the agent's own response with input from specialists
        to create a unified, coherent response for the user.
        
        Args:
            initial_response: The agent's initial response
            collaboration_results: Results from specialist collaborations
            original_message: The original user message
            context: Additional context information
            
        Returns:
            Dict containing the integrated response
        """
        if not collaboration_results:
            return initial_response
        
        try:
            # Default integration strategy: append specialist insights
            integrated_message = initial_response.get('message', '')
            
            if collaboration_results:
                integrated_message += "\n\n**Additional Insights:**\n"
                
                for result in collaboration_results:
                    if result.success and result.result:
                        integrated_message += f"\n**From {result.specialist_type}:** {result.result}\n"
            
            # Combine metadata
            integrated_metadata = initial_response.get('metadata', {})
            integrated_metadata['collaboration_results'] = [
                {
                    'specialist_type': result.specialist_type,
                    'success': result.success,
                    'metadata': result.metadata
                }
                for result in collaboration_results
            ]
            
            return {
                'message': integrated_message,
                'metadata': integrated_metadata
            }
            
        except Exception as e:
            logger.error(f"Error integrating responses: {e}")
            # Fallback to initial response
            return initial_response

    async def _handle_collaborations(self, collaboration_needs: List[CollaborationNeed],
                                   user_id: str, conversation_id: str, message: str,
                                   context: Optional[Dict[str, Any]] = None) -> List[CollaborationResult]:
        """
        Handle collaboration requests with user permission.

        Args:
            collaboration_needs: List of needed collaborations
            user_id: ID of the user
            conversation_id: ID of the conversation
            message: Original user message
            context: Additional context

        Returns:
            List of collaboration results
        """
        collaboration_results = []

        for need in collaboration_needs:
            try:
                # Check if auto-permission is enabled for this capability
                if need.required_capability in self.auto_permission_for_capabilities:
                    logger.info(f"Auto-permission enabled for capability: {need.required_capability}")
                    permission_granted = True
                else:
                    # Request user permission
                    permission_granted = await self.permission_manager.request_collaboration_permission(
                        user_id=user_id,
                        requesting_agent=self.agent_id,
                        specialist_type=need.specialist_type,
                        task_description=need.task_description,
                        required_capability=need.required_capability
                    )

                if permission_granted:
                    logger.info(f"Permission granted for collaboration with {need.specialist_type}")

                    # Find and collaborate with specialist
                    result = await self._collaborate_with_specialist(need, message, context)
                    collaboration_results.append(result)

                    # Record collaboration in history
                    self.collaboration_history.append({
                        'timestamp': datetime.now().isoformat(),
                        'conversation_id': conversation_id,
                        'specialist_type': need.specialist_type,
                        'success': result.success,
                        'user_id': user_id
                    })
                else:
                    logger.info(f"Permission denied for collaboration with {need.specialist_type}")
                    # Add a result indicating permission was denied
                    collaboration_results.append(CollaborationResult(
                        specialist_id="",
                        specialist_type=need.specialist_type,
                        result="User declined collaboration with this specialist.",
                        success=False,
                        metadata={'reason': 'permission_denied'}
                    ))

            except Exception as e:
                logger.error(f"Error handling collaboration with {need.specialist_type}: {e}")
                collaboration_results.append(CollaborationResult(
                    specialist_id="",
                    specialist_type=need.specialist_type,
                    result=f"Error occurred during collaboration: {str(e)}",
                    success=False,
                    metadata={'error': str(e)}
                ))

        return collaboration_results

    async def _collaborate_with_specialist(self, need: CollaborationNeed, message: str,
                                         context: Optional[Dict[str, Any]] = None) -> CollaborationResult:
        """
        Collaborate with a specialist agent.

        Args:
            need: The collaboration need
            message: Original user message
            context: Additional context

        Returns:
            CollaborationResult with the specialist's response
        """
        try:
            # Find the best specialist for this capability
            specialist = await self.network_tools.find_specialist(need.required_capability)

            if not specialist:
                return CollaborationResult(
                    specialist_id="",
                    specialist_type=need.specialist_type,
                    result=f"No available specialist found for {need.required_capability}",
                    success=False,
                    metadata={'reason': 'no_specialist_found'}
                )

            # Prepare collaboration context
            collaboration_context = {
                'original_message': message,
                'task_description': need.task_description,
                'requesting_agent': self.agent_id,
                'priority': need.priority,
                'additional_context': context or {}
            }

            if need.context:
                collaboration_context.update(need.context)

            # Consult with the specialist
            specialist_response = await self.network_tools.consult_specialist(
                specialist=specialist,
                query=message,
                context=collaboration_context
            )

            return CollaborationResult(
                specialist_id=specialist.agent_id,
                specialist_type=need.specialist_type,
                result=specialist_response,
                success=True,
                metadata={
                    'specialist_name': specialist.name,
                    'capability_used': need.required_capability,
                    'collaboration_timestamp': datetime.now().isoformat()
                }
            )

        except asyncio.TimeoutError:
            logger.error(f"Timeout collaborating with {need.specialist_type}")
            return CollaborationResult(
                specialist_id="",
                specialist_type=need.specialist_type,
                result="Collaboration timed out. Please try again.",
                success=False,
                metadata={'reason': 'timeout'}
            )
        except Exception as e:
            logger.error(f"Error collaborating with {need.specialist_type}: {e}")
            return CollaborationResult(
                specialist_id="",
                specialist_type=need.specialist_type,
                result=f"Collaboration failed: {str(e)}",
                success=False,
                metadata={'error': str(e)}
            )

    def get_capabilities(self) -> List[str]:
        """Get the list of capabilities this agent provides."""
        return self.capabilities.copy()

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this agent."""
        return {
            'agent_id': self.agent_id,
            'capabilities': self.capabilities,
            'is_active': self.is_active,
            'active_conversations': len(self.current_conversations),
            'total_collaborations': len(self.collaboration_history),
            'config': self.config
        }

    async def cleanup_conversation(self, conversation_id: str) -> None:
        """Clean up resources for a completed conversation."""
        if conversation_id in self.current_conversations:
            logger.info(f"Cleaning up conversation {conversation_id} for agent {self.agent_id}")
            del self.current_conversations[conversation_id]

    def __str__(self) -> str:
        return f"UserSelectedAgent(id={self.agent_id}, capabilities={self.capabilities})"

    def __repr__(self) -> str:
        return self.__str__()
