"""
User-Selected Analysis Agent

This module implements an AnalysisAgent that inherits from UserSelectedAgent,
providing user-controlled collaboration capabilities while maintaining the
analysis agent's core functionality of data analysis and visualization.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .user_selected_agent import UserSelectedAgent, CollaborationNeed
from ..nodes.unified_persona_node import UnifiedPersonaNode

logger = logging.getLogger(__name__)


class UserSelectedAnalysisAgent(UserSelectedAgent):
    """
    Analysis agent that can be directly selected by users.
    
    This agent provides:
    - Data analysis and statistical modeling
    - Data visualization and charting
    - Predictive analytics and insights
    - User-controlled collaboration with specialists
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the user-selected analysis agent.
        
        Args:
            config: Optional configuration dictionary
        """
        # Define analysis capabilities
        capabilities = [
            "data_analysis",
            "statistical_modeling",
            "data_visualization", 
            "predictive_analytics",
            "business_intelligence",
            "report_generation",
            "trend_analysis",
            "performance_metrics"
        ]
        
        super().__init__(
            agent_id="user_selected_analysis",
            capabilities=capabilities,
            config=config
        )
        
        # Initialize the underlying unified persona node for analysis functionality
        analysis_config = {
            "persona_id": "analysis",
            "agent_type": "analysis",
            "name": "Data Analysis Specialist",
            "description": "Expert data analyst specializing in insights, visualization, and statistical analysis",
            "strategy_id": "analysis",
            "capabilities": capabilities
        }
        
        # Merge with provided config
        if config:
            analysis_config.update(config)
        
        self.unified_node = UnifiedPersonaNode(
            persona_config=analysis_config,
            business_profile=config.get('business_profile') if config else None
        )
        
        # Analysis-specific configuration
        self.max_data_size = config.get('max_data_size', 10000000) if config else 10000000  # 10MB
        self.supported_formats = config.get('supported_formats', ['csv', 'xlsx', 'json', 'parquet']) if config else ['csv', 'xlsx', 'json', 'parquet']
        self.default_chart_types = config.get('default_chart_types', ['bar', 'line', 'scatter', 'histogram']) if config else ['bar', 'line', 'scatter', 'histogram']
        
        logger.info("UserSelectedAnalysisAgent initialized")

    async def handle_with_own_capabilities(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handle the user message using the analysis agent's own capabilities.
        
        This method provides:
        - Data analysis and statistical insights
        - Data visualization and charting
        - Trend analysis and pattern recognition
        - Performance metrics calculation
        
        Args:
            message: The user's message
            context: Additional context information
            
        Returns:
            Dict containing the analysis agent's response and metadata
        """
        try:
            logger.debug("Analysis agent handling message with own capabilities")
            
            # Prepare state for unified node processing
            state = {
                "messages": [{"role": "user", "content": message}],
                "user_id": context.get("user_id") if context else None,
                "conversation_id": context.get("conversation_id") if context else None,
                "business_profile": context.get("business_profile") if context else None,
                "metadata": context.get("metadata", {}) if context else {}
            }
            
            # Add analysis-specific context
            state["analysis_context"] = {
                "supported_formats": self.supported_formats,
                "max_data_size": self.max_data_size,
                "available_chart_types": self.default_chart_types
            }
            
            # Process with the unified persona node
            processed_state = await self.unified_node._process_message(state)
            
            # Extract response from processed state
            response_message = ""
            if "messages" in processed_state and processed_state["messages"]:
                # Get the last assistant message
                for msg in reversed(processed_state["messages"]):
                    if msg.get("role") == "assistant":
                        response_message = msg.get("content", "")
                        break
            
            if not response_message:
                response_message = "I'm your Data Analysis Specialist. I can help you analyze data, create visualizations, and generate insights. What data would you like to explore?"
            
            # Add analysis-specific enhancements
            enhanced_response = await self._enhance_analysis_response(message, response_message, context)
            
            return {
                "message": enhanced_response,
                "metadata": {
                    "agent_type": "analysis",
                    "capabilities_used": self._identify_used_capabilities(message),
                    "processing_method": "unified_persona_node",
                    "analysis_ready": self._is_analysis_ready(context),
                    "timestamp": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error in analysis own capabilities handling: {e}")
            return {
                "message": "I'm your Data Analysis Specialist! I can help you analyze data, create visualizations, perform statistical analysis, and generate insights. Please share your data or describe what you'd like to analyze.",
                "metadata": {
                    "error": str(e),
                    "fallback_response": True,
                    "timestamp": datetime.now().isoformat()
                }
            }

    async def assess_collaboration_needs(self, message: str, context: Optional[Dict[str, Any]] = None) -> List[CollaborationNeed]:
        """
        Assess whether collaboration with other agents is needed.
        
        The analysis agent determines if it needs help from specialists like
        marketing experts for campaign analysis, or visualization specialists
        for complex charts.
        
        Args:
            message: The user's message
            context: Additional context information
            
        Returns:
            List of CollaborationNeed objects
        """
        try:
            collaboration_needs = []
            message_lower = message.lower()
            
            # Check for marketing analysis needs
            marketing_analysis_keywords = [
                "marketing campaign", "campaign performance", "marketing roi", 
                "customer segmentation", "marketing metrics", "conversion analysis",
                "marketing attribution", "campaign optimization"
            ]
            if any(keyword in message_lower for keyword in marketing_analysis_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Marketing Expert",
                    task_description="provide marketing domain expertise for campaign analysis",
                    required_capability="marketing_strategy",
                    priority="normal",
                    context={"analysis_type": "marketing", "user_message": message}
                ))
            
            # Check for advanced visualization needs
            advanced_viz_keywords = [
                "interactive dashboard", "complex visualization", "3d chart",
                "animated chart", "custom visualization", "advanced plotting",
                "interactive plot", "dynamic visualization"
            ]
            if any(keyword in message_lower for keyword in advanced_viz_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Visualization Specialist",
                    task_description="create advanced interactive visualizations",
                    required_capability="advanced_visualization",
                    priority="normal",
                    context={"viz_complexity": "advanced", "user_message": message}
                ))
            
            # Check for content classification in analysis
            classification_analysis_keywords = [
                "text analysis", "sentiment analysis", "topic modeling",
                "content categorization", "document analysis", "text mining"
            ]
            if any(keyword in message_lower for keyword in classification_analysis_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Classification Specialist",
                    task_description="perform text analysis and content classification",
                    required_capability="text_classification",
                    priority="normal",
                    context={"analysis_type": "text", "user_message": message}
                ))
            
            # Check for business intelligence needs
            bi_keywords = [
                "business intelligence", "executive dashboard", "kpi analysis",
                "business metrics", "strategic analysis", "performance dashboard"
            ]
            if any(keyword in message_lower for keyword in bi_keywords):
                collaboration_needs.append(CollaborationNeed(
                    specialist_type="Business Intelligence Specialist",
                    task_description="provide business intelligence insights and strategic analysis",
                    required_capability="business_intelligence",
                    priority="high",
                    context={"analysis_type": "business_intelligence", "user_message": message}
                ))
            
            logger.debug(f"Analysis agent identified {len(collaboration_needs)} collaboration needs")
            return collaboration_needs
            
        except Exception as e:
            logger.error(f"Error assessing collaboration needs: {e}")
            return []

    async def _enhance_analysis_response(self, original_message: str, base_response: str, 
                                       context: Optional[Dict[str, Any]] = None) -> str:
        """
        Enhance the analysis response with additional helpful information.
        
        Args:
            original_message: The user's original message
            base_response: The base response from the unified node
            context: Additional context
            
        Returns:
            Enhanced response string
        """
        try:
            enhanced_response = base_response
            
            # Add data upload guidance if no data is present
            if not self._has_data_context(context):
                enhanced_response += f"\n\n📊 **Data Upload**: I can analyze data in these formats: {', '.join(self.supported_formats)}. Please upload your data file or provide a data source."
            
            # Add visualization suggestions for data analysis requests
            if self._is_visualization_request(original_message):
                enhanced_response += f"\n\n📈 **Visualization Options**: I can create {', '.join(self.default_chart_types)} charts and more. What type of visualization would work best for your data?"
            
            # Add statistical analysis guidance
            if self._is_statistical_request(original_message):
                enhanced_response += "\n\n🔢 **Statistical Analysis**: I can perform descriptive statistics, correlation analysis, regression modeling, and hypothesis testing. What specific analysis are you looking for?"
            
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Error enhancing analysis response: {e}")
            return base_response

    def _identify_used_capabilities(self, message: str) -> List[str]:
        """Identify which capabilities were likely used based on the message."""
        used_capabilities = ["data_analysis"]  # Always include base capability
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["chart", "plot", "graph", "visualize"]):
            used_capabilities.append("data_visualization")
        
        if any(word in message_lower for word in ["predict", "forecast", "model"]):
            used_capabilities.append("predictive_analytics")
        
        if any(word in message_lower for word in ["trend", "pattern", "time series"]):
            used_capabilities.append("trend_analysis")
        
        if any(word in message_lower for word in ["report", "summary", "dashboard"]):
            used_capabilities.append("report_generation")
        
        return used_capabilities

    def _is_analysis_ready(self, context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if the agent is ready to perform analysis."""
        if not context:
            return False
        return context.get("has_data", False) or context.get("data_source", False)

    def _has_data_context(self, context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if data context is available."""
        if not context:
            return False
        return context.get("has_data", False) or context.get("uploaded_files", [])

    def _is_visualization_request(self, message: str) -> bool:
        """Check if the message is requesting visualization."""
        viz_keywords = ["chart", "graph", "plot", "visualize", "show", "display"]
        return any(keyword in message.lower() for keyword in viz_keywords)

    def _is_statistical_request(self, message: str) -> bool:
        """Check if the message is requesting statistical analysis."""
        stats_keywords = ["statistics", "correlation", "regression", "hypothesis", "significance", "p-value"]
        return any(keyword in message.lower() for keyword in stats_keywords)

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this analysis agent."""
        base_info = super().get_agent_info()
        base_info.update({
            "agent_type": "analysis",
            "specialization": "data_analysis_and_visualization",
            "supported_formats": self.supported_formats,
            "max_data_size": self.max_data_size,
            "default_chart_types": self.default_chart_types,
            "supports_statistical_analysis": True,
            "supports_predictive_analytics": True
        })
        return base_info
