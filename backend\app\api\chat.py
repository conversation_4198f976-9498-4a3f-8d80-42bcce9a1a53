"""
Chat API endpoints for the Datagenius backend.

This module provides API endpoints for chat functionality.
"""

import logging
import uuid
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..utils.json_utils import ensure_serializable, sanitize_metadata, sanitize_json

from ..models.chat import (
    ConversationCreate, ConversationUpdate, ConversationResponse, ConversationListResponse,
    MessageCreate, MessageResponse, SendMessageRequest, SendMessageResponse
)


from ..models.auth import User
from ..database import (
    get_db, create_conversation, get_conversation, get_user_conversations,
    update_conversation, delete_conversation, create_message, get_conversation_messages,
    update_message, get_message, Message, Conversation, create_message_edit,
    get_message_edit_history, get_message_thread, get_message_children
)
from ..auth import get_current_active_user, get_current_user_from_token

# Import LangGraph agent factory
from agents.langgraph.core.agent_factory import agent_factory

# Import LangGraph workflow manager
from agents.langgraph.core.workflow_manager import WorkflowManager
from agents.langgraph.states.unified_state import create_unified_state

# Configure logging
logger = logging.getLogger(__name__)

# Import enhanced marketing agent systems
try:
    from backend.agents.marketing_agent.exceptions import error_handler
    from backend.agents.marketing_agent.analytics import get_analytics, EventType
    from backend.agents.marketing_agent.monitoring import performance_monitor
    ENHANCED_MARKETING_AVAILABLE = True
    logger.info("Enhanced marketing agent systems imported successfully")
except ImportError as e:
    logger.warning(f"Enhanced marketing agent systems not available: {e}")
    ENHANCED_MARKETING_AVAILABLE = False
    # Create fallback objects
    error_handler = None
    get_analytics = lambda: None
    EventType = None
    performance_monitor = lambda x: lambda f: f  # No-op decorator

# Create router
router = APIRouter(prefix="/chat", tags=["Chat"])

# Create LangGraph workflow manager instance
workflow_manager = WorkflowManager()

# Initialize workflow manager with agents
def initialize_workflow_manager():
    """Initialize workflow manager with agent nodes."""
    try:
        # Create and register agent nodes
        agents = agent_factory.create_all_agents()

        if not agents:
            logger.error("❌ No agents created by agent factory - this is a critical error")
            # Try to register default agents as fallback
            agent_factory._register_default_agents()
            agents = agent_factory.create_all_agents()

        if not agents:
            logger.error("❌ Still no agents available after fallback registration")
            raise ValueError("No agents available for workflow manager initialization")

        for agent_id, agent_node in agents.items():
            workflow_manager.register_agent_node(agent_id, agent_node)

        logger.info(f"✅ Initialized workflow manager with {len(agents)} agents: {list(agents.keys())}")

        # Verify agents are properly registered
        registered_agents = list(workflow_manager.agent_nodes.keys())
        if not registered_agents:
            logger.error("❌ No agents registered in workflow manager after initialization")
            raise ValueError("Agent registration failed")
        else:
            logger.info(f"✅ Verified {len(registered_agents)} agents registered in workflow manager")

    except Exception as e:
        logger.error(f"❌ Error initializing workflow manager: {e}", exc_info=True)
        raise

# Initialize the workflow manager
initialize_workflow_manager()


@router.post("/conversations", response_model=ConversationResponse)
async def create_new_conversation(
    conversation: ConversationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new conversation.

    Args:
        conversation: Conversation creation request

    Returns:
        Created conversation
    """
    logger.info(f"User {current_user.id} creating new conversation with persona {conversation.persona_id}")

    # Enhanced logging for debugging
    registered_agents = agent_factory.get_available_agents()
    logger.info(f"Registered agents in AgentFactory: {registered_agents}")

    # Check if the agent exists in the factory
    if conversation.persona_id not in registered_agents:
        logger.error(f"Persona {conversation.persona_id} not found in registry")

        # Check if the persona exists in the database
        from ..database import get_persona
        db_persona = get_persona(db, conversation.persona_id)
        if db_persona:
            logger.error(f"Persona {conversation.persona_id} exists in database but not in registry: {db_persona.name}")

        # Check if the user has purchased this persona
        from ..database import has_user_purchased_persona
        is_purchased = has_user_purchased_persona(db, current_user.id, conversation.persona_id)
        logger.error(f"Has user purchased persona {conversation.persona_id}: {is_purchased}")

        # Check agent factory configuration
        config = agent_factory.get_agent_config(conversation.persona_id)
        logger.error(f"Agent configuration for {conversation.persona_id}: {config}")

        # Check agent node
        agent_node = agent_factory.create_agent_node(conversation.persona_id)
        logger.error(f"Agent node for {conversation.persona_id}: {agent_node}")

        raise HTTPException(status_code=404, detail=f"Persona {conversation.persona_id} not found")

    # Create the conversation
    logger.info(f"Attempting to create conversation '{conversation.title}' for user {current_user.id} with persona {conversation.persona_id}") # Log before create
    try: # Added missing try keyword
        db_conversation = create_conversation(
            db,
            user_id=current_user.id,
            title=conversation.title,
            persona_id=conversation.persona_id,
            metadata=conversation.metadata
        )
        logger.info(f"Successfully created conversation with ID: {db_conversation.id}") # Log after create
    except Exception as e:
        logger.error(f"Database error during create_conversation: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Database error creating conversation.")


    # Enqueue an initiation task to generate the first greeting message
    logger.info(f"Attempting to enqueue initiation task for conversation {db_conversation.id} with persona {conversation.persona_id}") # Log before enqueue
    try: # Corrected try block for enqueue
        from ..queue import message_queue
        await message_queue.enqueue(
            user_id=current_user.id, # Pass user_id for context if needed by agent later
            conversation_id=db_conversation.id,
            message="", # No user message for initiation
            context={
                "task_type": "initiate",
                "persona_id": conversation.persona_id,
                "current_persona": conversation.persona_id,  # Ensure persona context is preserved
                "user_name": getattr(current_user, 'name', '') or getattr(current_user, 'username', '')
            }, # Special context
            metadata={}
        )
        logger.info(f"Successfully enqueued initiation task for conversation {db_conversation.id} with persona {conversation.persona_id}") # Log after enqueue
    except Exception as e:
        # Log the error but don't fail the conversation creation
        logger.error(f"Failed to enqueue initiation task for conversation {db_conversation.id}: {e}", exc_info=True)
        # Optionally, decide if this failure should prevent returning success
        # For now, we log but still return the created conversation


    return ConversationResponse(
        id=db_conversation.id,
        user_id=db_conversation.user_id,
        persona_id=db_conversation.persona_id,
        title=db_conversation.title,
        created_at=db_conversation.created_at,
        updated_at=db_conversation.updated_at,
        is_archived=db_conversation.is_archived,
        messages=[]
    )


@router.get("/conversations", response_model=ConversationListResponse)
async def list_conversations(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    List all conversations for the current user.

    Args:
        skip: Number of conversations to skip
        limit: Maximum number of conversations to return

    Returns:
        List of conversations
    """
    logger.info(f"User {current_user.id} listing conversations")

    # Get conversations for the user
    conversations = get_user_conversations(db, current_user.id, skip, limit)

    # Count total conversations for the user
    total = db.query(Conversation).filter(
        Conversation.user_id == current_user.id,
        Conversation.is_archived == False
    ).count()

    return ConversationListResponse(
        conversations=[
            ConversationResponse(
                id=conv.id,
                user_id=conv.user_id,
                persona_id=conv.persona_id,
                title=conv.title,
                created_at=conv.created_at,
                updated_at=conv.updated_at,
                is_archived=conv.is_archived,
                messages=[]
            ) for conv in conversations
        ],
        total=total
    )


@router.get("/conversations/{conversation_id}", response_model=ConversationResponse)
async def get_conversation_detail(
    conversation_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get details of a specific conversation.

    Args:
        conversation_id: ID of the conversation

    Returns:
        Conversation details
    """
    logger.info(f"User {current_user.id} getting conversation {conversation_id}")

    # Get the conversation
    conversation = get_conversation(db, conversation_id)

    # Check if the conversation exists and belongs to the user
    if not conversation or conversation.user_id != current_user.id:
        logger.error(f"Conversation {conversation_id} not found or does not belong to user {current_user.id}")
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Get messages for the conversation
    messages = get_conversation_messages(db, conversation_id)

    # Enhanced logging for debugging message retrieval
    logger.info(f"Retrieved {len(messages)} messages for conversation {conversation_id}")
    for i, msg in enumerate(messages):
        logger.info(f"Message {i+1}: ID={msg.id}, sender={msg.sender}, content_length={len(msg.content) if msg.content else 0}, created_at={msg.created_at}")

    return ConversationResponse(
        id=conversation.id,
        user_id=conversation.user_id,
        persona_id=conversation.persona_id,
        title=conversation.title,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at,
        is_archived=conversation.is_archived,
        messages=[
            MessageResponse(
                id=msg.id,
                conversation_id=msg.conversation_id,
                sender=msg.sender,
                content=msg.content,
                metadata=msg.message_metadata,
                created_at=msg.created_at
            ) for msg in messages
        ]
    )


@router.put("/conversations/{conversation_id}", response_model=ConversationResponse)
async def update_conversation_details(
    conversation_id: str,
    update_data: ConversationUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Update a conversation's details (title, persona_id, etc.).

    Args:
        conversation_id: ID of the conversation
        update_data: Data to update

    Returns:
        Updated conversation
    """
    logger.info(f"User {current_user.id} updating conversation {conversation_id}")

    # Get the conversation
    conversation = get_conversation(db, conversation_id)

    # Check if the conversation exists and belongs to the user
    if not conversation or conversation.user_id != current_user.id:
        logger.error(f"Conversation {conversation_id} not found or does not belong to user {current_user.id}")
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Prepare update data
    update_dict = {}
    if update_data.title is not None:
        update_dict["title"] = update_data.title
    if update_data.is_archived is not None:
        update_dict["is_archived"] = update_data.is_archived
    if update_data.metadata is not None:
        update_dict["metadata"] = update_data.metadata
    if hasattr(update_data, 'persona_id') and update_data.persona_id is not None:
        update_dict["persona_id"] = update_data.persona_id
        logger.info(f"🎯 API: Updating conversation {conversation_id} persona_id to {update_data.persona_id}")

    # Update the conversation
    updated_conversation = update_conversation(db, conversation_id, update_data=update_dict)

    # Get messages for the conversation
    messages = get_conversation_messages(db, conversation_id)

    return ConversationResponse(
        id=updated_conversation.id,
        user_id=updated_conversation.user_id,
        persona_id=updated_conversation.persona_id,
        title=updated_conversation.title,
        created_at=updated_conversation.created_at,
        updated_at=updated_conversation.updated_at,
        is_archived=updated_conversation.is_archived,
        messages=[
            MessageResponse(
                id=msg.id,
                conversation_id=msg.conversation_id,
                sender=msg.sender,
                content=msg.content,
                metadata=msg.message_metadata,
                created_at=msg.created_at
            ) for msg in messages
        ]
    )




@router.delete("/conversations/{conversation_id}")
async def delete_conversation_endpoint(
    conversation_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete a conversation.

    Args:
        conversation_id: ID of the conversation

    Returns:
        Success message
    """
    logger.info(f"User {current_user.id} deleting conversation {conversation_id}")

    # Get the conversation
    conversation = get_conversation(db, conversation_id)

    # Check if the conversation exists and belongs to the user
    if not conversation or conversation.user_id != current_user.id:
        logger.error(f"Conversation {conversation_id} not found or does not belong to user {current_user.id}")
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Delete the conversation
    success = delete_conversation(db, conversation_id)

    if not success:
        logger.error(f"Failed to delete conversation {conversation_id}")
        raise HTTPException(status_code=500, detail="Failed to delete conversation")

    return {"message": "Conversation deleted successfully"}


@router.post("/messages", response_model=MessageResponse)
async def create_new_message(
    message: MessageCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new message.

    Args:
        message: Message creation request

    Returns:
        Created message
    """
    logger.info(f"User {current_user.id} creating new message in conversation {message.conversation_id}")

    # Get the conversation
    conversation = get_conversation(db, message.conversation_id)

    # Check if the conversation exists and belongs to the user
    if not conversation or conversation.user_id != current_user.id:
        logger.error(f"Conversation {message.conversation_id} not found or does not belong to user {current_user.id}")
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Create the message
    message_data = {
        "id": str(uuid.uuid4()),
        "conversation_id": message.conversation_id,
        "sender": "user",  # Messages created via this endpoint are always from the user
        "content": message.content,
        "message_metadata": message.metadata
    }

    db_message = create_message(
        db,
        conversation_id=message_data["conversation_id"],
        sender=message_data["sender"],
        content=message_data["content"],
        metadata=message_data.get("message_metadata")
    )

    # Update the conversation's updated_at timestamp
    # Just trigger the updated_at timestamp without changing any data
    update_conversation(db, message.conversation_id)

    return MessageResponse(
        id=db_message.id,
        conversation_id=db_message.conversation_id,
        sender=db_message.sender,
        content=db_message.content,
        metadata=db_message.message_metadata,
        created_at=db_message.created_at
    )


@router.post("/send", response_model=SendMessageResponse)
@performance_monitor("send_message_to_agent")
async def send_message_to_agent(
    request: SendMessageRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Send a message to an agent and get a response.

    Args:
        request: Message sending request

    Returns:
        User message and agent response
    """
    logger.info(f"User {current_user.id} sending message to agent in conversation {request.conversation_id}")

    # Get the conversation
    conversation = get_conversation(db, request.conversation_id)

    # Check if the conversation exists and belongs to the user
    if not conversation or conversation.user_id != current_user.id:
        logger.error(f"Conversation {request.conversation_id} not found or does not belong to user {current_user.id}")
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Create the user message
    user_message_id = str(uuid.uuid4())
    user_message_data = {
        "id": user_message_id,
        "conversation_id": request.conversation_id,
        "sender": "user",
        "content": request.message,
        "metadata": {}
    }

    db_user_message = create_message(
        db,
        conversation_id=user_message_data["conversation_id"],
        sender=user_message_data["sender"],
        content=user_message_data["content"],
        metadata=user_message_data.get("metadata")
    )

    # Create an initial empty AI message
    ai_message_id = str(uuid.uuid4())

    # Create initial metadata and sanitize it
    initial_metadata = {
        "status": "processing",
        "processing": True
    }

    # Sanitize metadata to ensure it's JSON serializable
    sanitized_initial_metadata = sanitize_json(initial_metadata)

    initial_ai_message_data = {
        "id": ai_message_id,
        "conversation_id": request.conversation_id,
        "sender": "ai",
        "content": "Processing your request...",  # Initial message
        "message_metadata": sanitized_initial_metadata
    }

    db_ai_message = create_message(
        db,
        conversation_id=initial_ai_message_data["conversation_id"],
        sender=initial_ai_message_data["sender"],
        content=initial_ai_message_data["content"],
        metadata=initial_ai_message_data.get("message_metadata")
    )

    # Update the conversation's updated_at timestamp
    update_conversation(db, request.conversation_id)

    # Call the orchestrator directly
    try:
        # Enhance context with conversation history for better context preservation
        enhanced_context = request.context.copy() if request.context else {}

        # Add user and conversation identifiers to context
        enhanced_context["user_id"] = current_user.id
        enhanced_context["conversation_id"] = request.conversation_id

        # Get recent conversation history to preserve file context
        try:
            recent_messages = get_conversation_messages(db, request.conversation_id, limit=10)
            conversation_history = []
            latest_data_source = None

            for msg in recent_messages:
                message_data = {
                    "sender": msg.sender,
                    "content": msg.content,
                    "metadata": msg.message_metadata or {}
                }
                conversation_history.append(message_data)

                # Extract data source context from message metadata
                if msg.message_metadata and "data_source" in msg.message_metadata:
                    latest_data_source = msg.message_metadata["data_source"]

            enhanced_context["conversation_history"] = conversation_history

            # Preserve data source context if found and not already present
            if latest_data_source and "data_source" not in enhanced_context:
                enhanced_context["data_source"] = latest_data_source
                logger.info(f"Preserved data source context from conversation history: {latest_data_source}")

        except Exception as e:
            logger.error(f"Error retrieving conversation history for context: {str(e)}")
            # Continue without conversation history if there's an error

        # Create unified state for LangGraph workflow
        initial_state = create_unified_state(
            user_id=str(current_user.id),
            conversation_id=request.conversation_id,
            workflow_type="chat_message",
            initial_message={
                "content": request.message,
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
        )

        # Set current_persona to ensure proper routing
        initial_state["current_persona"] = conversation.persona_id
        logger.info(f"🎯 ChatAPI: Set current_persona in state: {conversation.persona_id}")

        # Set selected_agent and conversation_mode for proper LangGraph routing
        from agents.langgraph.states.unified_state import ConversationMode, set_selected_agent
        initial_state = set_selected_agent(
            initial_state,
            conversation.persona_id,
            ConversationMode.CONVERSATION,
            {"persona": conversation.persona_id, "context": "user_selected"}
        )
        logger.info(f"🎯 ChatAPI: Set selected_agent and conversation_mode for {conversation.persona_id}")

        # Add enhanced context to state
        initial_state["conversation_context"].update(enhanced_context)

        # Execute workflow using LangGraph
        workflow_result = await workflow_manager.execute_workflow(
            state=initial_state,
            workflow_type="chat_message"
        )

        # Extract response from workflow result
        final_content = "No response generated."
        final_metadata = {
            "status": "completed",
            "processing": False
        }

        if workflow_result and "messages" in workflow_result:
            # Get the last agent message
            agent_messages = [msg for msg in workflow_result["messages"] if msg.get("type") == "agent"]
            if agent_messages:
                last_message = agent_messages[-1]
                final_content = last_message.get("content", final_content)
                final_metadata.update(last_message.get("metadata", {}))

        # Enhanced logging for debugging AI message updates
        logger.info(f"LangGraph workflow response for message {ai_message_id}: content_length={len(final_content)}, metadata_keys={list(final_metadata.keys())}")
        logger.info(f"About to update AI message {ai_message_id} with content: '{final_content[:100]}...'")
        logger.info(f"AI message metadata: {final_metadata}")

        # Track successful message processing
        if ENHANCED_MARKETING_AVAILABLE and get_analytics:
            try:
                analytics = get_analytics()
                if analytics:
                    analytics.track_event(
                        event_type=EventType.MESSAGE_PROCESSED,
                        user_id=str(current_user.id),
                        conversation_id=request.conversation_id,
                        metadata={
                            'message_length': len(request.message),
                            'response_length': len(final_content),
                            'has_context': bool(request.context),
                            'persona_id': conversation.persona_id
                        }
                    )
            except Exception as analytics_error:
                logger.error(f"Analytics tracking failed: {analytics_error}")

        # Preserve data source context in AI message metadata for future reference
        if "data_source" in enhanced_context:
            final_metadata["data_source"] = enhanced_context["data_source"]
            logger.info(f"Preserved data source in AI message metadata: {enhanced_context['data_source']}")

        # Also preserve in user message metadata if context was provided
        if request.context and "data_source" in request.context:
            try:
                user_message_metadata = {"data_source": request.context["data_source"]}
                # Update the user message with data source metadata
                from ..database import update_message
                update_message(db, db_user_message.id, metadata=user_message_metadata)
                logger.info(f"Updated user message metadata with data source: {request.context['data_source']}")
            except Exception as e:
                logger.error(f"Error updating user message metadata: {str(e)}")

        # Sanitize metadata to ensure it's JSON serializable
        sanitized_metadata = sanitize_json(final_metadata)

        logger.info(f"Sanitized metadata for message {ai_message_id}")

        updated_message = update_message(
            db,
            ai_message_id,
            content=final_content,
            metadata=sanitized_metadata
        )

        if not updated_message:
             # If update failed (e.g., message ID mismatch), log error and use original ID
             logger.error(f"❌ Failed to update AI message {ai_message_id}, attempting to update original DB object.")
             db_ai_message.content = final_content
             db_ai_message.message_metadata = sanitized_metadata
             try:
                 db.commit()
                 db.refresh(db_ai_message)
                 updated_message = db_ai_message
                 logger.info(f"✅ Successfully updated original AI message {ai_message_id} via fallback.")
             except Exception as commit_exc:
                 logger.error(f"❌ Fallback update commit failed for AI message {ai_message_id}: {commit_exc}", exc_info=True)
                 db.rollback()
                 # If even fallback fails, we might have to stick with the original db_ai_message content or an error state
                 # For now, ensure updated_message is at least the db_ai_message object
                 updated_message = db_ai_message
                 updated_message.content = "Error: Could not finalize AI response." # Indicate an error
                 updated_message.message_metadata = {"error": "update_fallback_failed"}
        else:
             logger.info(f"✅ Successfully updated AI message {ai_message_id} with final content: '{final_content[:100]}...'")
             logger.info(f"✅ Updated message content length: {len(updated_message.content)}, sender: {updated_message.sender}")


    except Exception as e:
        logger.error(f"Error during LangGraph workflow processing: {str(e)}", exc_info=True)

        # Use enhanced error handling if available
        if ENHANCED_MARKETING_AVAILABLE and error_handler:
            try:
                error_context = {
                    'user_id': str(current_user.id),
                    'conversation_id': request.conversation_id,
                    'message': request.message,
                    'enhanced_context': enhanced_context
                }

                error_response = error_handler.handle_exception(e, error_context)
                error_message = error_response.get("message", "An error occurred while processing your message.")
                error_metadata = error_response.get("metadata", {})

                # Track error event
                analytics = get_analytics()
                if analytics:
                    analytics.track_error(
                        error=e,
                        user_id=str(current_user.id),
                        conversation_id=request.conversation_id,
                        context={'message': request.message}
                    )

                logger.info("Used enhanced error handling for orchestrator error")

            except Exception as error_handling_error:
                logger.error(f"Enhanced error handling failed: {error_handling_error}")
                # Fallback to basic error handling
                error_message = "An error occurred while processing your message. Please try again later."
                error_metadata = {
                    "status": "error",
                    "processing": False,
                    "error": str(e),
                    "error_type": e.__class__.__name__
                }
        else:
            # Basic error handling
            error_message = "An error occurred while processing your message. Please try again later."
            error_metadata = {
                "status": "error",
                "processing": False,
                "error": str(e),
                "error_type": e.__class__.__name__
            }

        # Sanitize metadata to ensure it's JSON serializable
        sanitized_error_metadata = sanitize_json(error_metadata)

        logger.info(f"Sanitized error metadata for message {ai_message_id}")

        updated_message = update_message(
            db,
            ai_message_id,
            content=error_message,
            metadata=sanitized_error_metadata
        )
        if not updated_message:
             logger.error(f"Failed to update AI message {ai_message_id} with error, using original DB object.")
             updated_message = db_ai_message
             updated_message.content = error_message
             updated_message.message_metadata = sanitized_error_metadata


    # Return the final response including the updated AI message
    return SendMessageResponse(
        conversation_id=request.conversation_id,
        user_message=MessageResponse(
            id=db_user_message.id,
            conversation_id=db_user_message.conversation_id,
            sender=db_user_message.sender,
            content=db_user_message.content,
            metadata=db_user_message.message_metadata,
            created_at=db_user_message.created_at
        ),
        ai_message=MessageResponse(
            id=updated_message.id,
            conversation_id=updated_message.conversation_id,
            sender=updated_message.sender,
            content=updated_message.content,
            metadata=updated_message.message_metadata,
            created_at=updated_message.created_at
        )
    )


async def handle_streaming_response(
    db: Session,
    workflow_manager: WorkflowManager,
    user_id: int,
    conversation_id: str,
    message: str,
    context: Dict[str, Any],
    ai_message_id: str,
    manager
):
    """
    Handle streaming LLM response through WebSocket.

    Args:
        db: Database session
        orchestrator: Orchestrator instance
        user_id: User ID
        conversation_id: Conversation ID
        message: User message
        context: Enhanced context
        ai_message_id: AI message ID for streaming
        manager: WebSocket connection manager
    """
    try:
        logger.debug(f"🚀 handle_streaming_response: Starting for message {ai_message_id}")
        logger.debug(f"🎯 handle_streaming_response: Broadcasting to conversation {conversation_id}")

        # Send stream start event
        await manager.broadcast({
            "type": "stream_start",
            "message_id": ai_message_id,
            "conversation_id": conversation_id,
            "metadata": {
                "streaming": True,
                "status": "streaming"
            }
        }, conversation_id)
        logger.debug(f"📡 handle_streaming_response: Sent stream_start for message {ai_message_id}")

        # Initialize streaming content
        streaming_content = ""
        final_metadata = {
            "status": "streaming",
            "processing": True,
            "streaming": True,
            "complete": False
        }

        # Call orchestrator with streaming enabled
        context["streaming_enabled"] = True
        context["current_message_id"] = ai_message_id  # Add message ID for tool status notifications
        context["stream_callback"] = lambda chunk: handle_stream_chunk(
            chunk, ai_message_id, conversation_id, manager
        )

        # Process message with LangGraph workflow streaming
        logger.debug(f"📡 handle_streaming_response: Starting LangGraph streaming workflow for message {ai_message_id}")

        # Create unified state for workflow
        initial_state = create_unified_state(
            user_id=str(user_id),
            conversation_id=conversation_id,
            workflow_type="chat_message",
            initial_message={
                "content": message,
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
        )

        # Add context to state
        initial_state.update(context)
        initial_state["streaming_enabled"] = True

        # Execute workflow with streaming
        chunk_count = 0
        async for chunk_data in workflow_manager.execute_workflow_streaming(
            state=initial_state,
            workflow_type="chat_message"
        ):
            chunk_count += 1
            # Only log every 20th chunk to reduce verbosity
            if chunk_count % 20 == 0:
                logger.debug(f"📦 handle_streaming_response: Received chunk {chunk_count}: {chunk_data.get('type')} - {chunk_data.get('content', '')[:50]}...")

            if chunk_data.get("type") == "content":
                chunk_content = chunk_data.get("content", "")
                streaming_content += chunk_content

                # Send stream chunk
                await manager.broadcast({
                    "type": "stream_chunk",
                    "message_id": ai_message_id,
                    "content": chunk_content,
                    "conversation_id": conversation_id
                }, conversation_id)
                # Removed per-chunk logging to reduce verbosity

            elif chunk_data.get("type") == "metadata":
                # Update metadata during streaming
                chunk_metadata = chunk_data.get("metadata", {})
                final_metadata.update(chunk_metadata)
                logger.info(f"📋 Streaming: Received metadata chunk with keys: {list(chunk_metadata.keys())}")
                if 'is_persona_recommendation' in chunk_metadata:
                    logger.info(f"📋 Streaming: Received persona recommendation metadata")
                if 'recommended_personas' in chunk_metadata:
                    logger.info(f"📋 Streaming: Received {len(chunk_metadata['recommended_personas'])} recommended personas")

        # Turn off typing indicator
        await manager.send_typing_indicator(conversation_id, False)

        # Finalize metadata - preserve existing metadata and only update status fields
        status_metadata = {
            "status": "completed",
            "processing": False,
            "streaming": False,
            "complete": True
        }
        final_metadata.update(status_metadata)

        # Log final metadata for debugging
        logger.info(f"Streaming: Final metadata keys before database update: {list(final_metadata.keys())}")
        if 'is_persona_recommendation' in final_metadata:
            logger.info(f"Streaming: Final metadata has is_persona_recommendation: {final_metadata['is_persona_recommendation']}")
        if 'recommended_personas' in final_metadata:
            logger.info(f"Streaming: Final metadata has {len(final_metadata['recommended_personas'])} recommended personas")

        # Update the AI message in database with final content
        sanitized_final_metadata = sanitize_json(final_metadata)
        update_message(db, ai_message_id, streaming_content, sanitized_final_metadata)

        # Send stream end event
        await manager.broadcast({
            "type": "stream_end",
            "message_id": ai_message_id,
            "conversation_id": conversation_id,
            "metadata": sanitized_final_metadata
        }, conversation_id)

        # Send final complete message
        db_ai_message = get_message(db, ai_message_id)
        if db_ai_message:
            await manager.broadcast({
                "type": "ai_message_complete",
                "message": {
                    "id": db_ai_message.id,
                    "conversation_id": db_ai_message.conversation_id,
                    "sender": db_ai_message.sender,
                    "content": db_ai_message.content,
                    "metadata": db_ai_message.message_metadata,
                    "created_at": db_ai_message.created_at.isoformat()
                }
            }, conversation_id)

        # Update conversation timestamp
        update_conversation(db, conversation_id)

    except Exception as e:
        logger.error(f"Error in streaming response: {str(e)}")

        # Turn off typing indicator
        await manager.send_typing_indicator(conversation_id, False)

        # Send error message
        error_content = f"I apologize, but I encountered an error while processing your request: {str(e)}"
        error_metadata = {
            "status": "error",
            "processing": False,
            "streaming": False,
            "complete": True,
            "error": str(e)
        }

        # Update message with error
        sanitized_error_metadata = sanitize_json(error_metadata)
        update_message(db, ai_message_id, error_content, sanitized_error_metadata)

        # Send error event
        await manager.broadcast({
            "type": "ai_message_error",
            "message": {
                "id": ai_message_id,
                "conversation_id": conversation_id,
                "sender": "ai",
                "content": error_content,
                "metadata": sanitized_error_metadata,
                "created_at": datetime.now().isoformat()
            }
        }, conversation_id)


async def handle_stream_chunk(chunk: str, message_id: str, conversation_id: str, manager):
    """Helper function to handle individual stream chunks."""
    try:
        await manager.broadcast({
            "type": "stream_chunk",
            "message_id": message_id,
            "content": chunk,
            "conversation_id": conversation_id
        }, conversation_id)
    except Exception as e:
        logger.error(f"Error sending stream chunk: {str(e)}")


# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: dict[str, List[WebSocket]] = {}
        self.connection_status: dict[str, dict[WebSocket, bool]] = {}  # Track connection status

    async def connect(self, websocket: WebSocket, conversation_id: str):
        try:
            logger.info(f"Accepting WebSocket connection for conversation {conversation_id}")
            await websocket.accept()

            # Initialize connection tracking for this conversation if needed
            if conversation_id not in self.active_connections:
                logger.info(f"Creating new connection tracking for conversation {conversation_id}")
                self.active_connections[conversation_id] = []
                self.connection_status[conversation_id] = {}

            # Add this connection to the active connections
            self.active_connections[conversation_id].append(websocket)
            self.connection_status[conversation_id][websocket] = True

            # Log connection count
            connection_count = len(self.active_connections[conversation_id])
            logger.info(f"Added WebSocket connection for conversation {conversation_id}. Total connections: {connection_count}")

            # Send connection confirmation
            await websocket.send_json({
                "type": "connection_status",
                "status": "connected",
                "conversation_id": conversation_id,
                "timestamp": datetime.now().isoformat()
            })

            logger.info(f"WebSocket connection established for conversation {conversation_id}")
        except Exception as e:
            logger.error(f"Error accepting WebSocket connection for conversation {conversation_id}: {str(e)}", exc_info=True)
            raise

    def disconnect(self, websocket: WebSocket, conversation_id: str):
        try:
            logger.info(f"Disconnecting WebSocket for conversation {conversation_id}")

            if conversation_id in self.active_connections:
                # Check if this WebSocket is in the active connections for this conversation
                if websocket in self.active_connections[conversation_id]:
                    logger.info(f"Removing WebSocket from active connections for conversation {conversation_id}")
                    self.active_connections[conversation_id].remove(websocket)

                    # Log remaining connection count
                    remaining_connections = len(self.active_connections[conversation_id])
                    logger.info(f"Remaining connections for conversation {conversation_id}: {remaining_connections}")

                    # Update connection status
                    if conversation_id in self.connection_status and websocket in self.connection_status[conversation_id]:
                        logger.info(f"Updating connection status for conversation {conversation_id}")
                        self.connection_status[conversation_id][websocket] = False
                        del self.connection_status[conversation_id][websocket]
                else:
                    logger.warning(f"WebSocket not found in active connections for conversation {conversation_id}")

                # Clean up empty conversation entries
                if not self.active_connections[conversation_id]:
                    logger.info(f"No more active connections for conversation {conversation_id}, cleaning up")
                    del self.active_connections[conversation_id]
                    if conversation_id in self.connection_status:
                        del self.connection_status[conversation_id]
            else:
                logger.warning(f"Conversation {conversation_id} not found in active connections")

            logger.info(f"WebSocket connection closed for conversation {conversation_id}")
        except Exception as e:
            logger.error(f"Error during WebSocket disconnection for conversation {conversation_id}: {str(e)}", exc_info=True)

    async def broadcast(self, message: Dict[str, Any], conversation_id: str):
        """
        Broadcast a message to all clients connected to a conversation.

        Args:
            message: The message to broadcast
            conversation_id: ID of the conversation
        """
        # Only log non-stream_chunk messages to reduce verbosity
        message_type = message.get('type', 'unknown')
        if message_type != 'stream_chunk':
            logger.debug(f"🔊 ConnectionManager.broadcast: Attempting to broadcast {message_type} to conversation {conversation_id}")
            logger.debug(f"🔊 Active conversations: {list(self.active_connections.keys())}")
            logger.debug(f"🔊 Connection count for {conversation_id}: {len(self.active_connections.get(conversation_id, []))}")

        if conversation_id in self.active_connections:
            disconnected = []

            # Ensure the message is JSON serializable
            serializable_message = ensure_serializable(message)

            # If the message contains metadata, sanitize it
            if "message" in serializable_message and "metadata" in serializable_message["message"]:
                serializable_message["message"]["metadata"] = sanitize_metadata(
                    serializable_message["message"]["metadata"]
                )

            for connection in self.active_connections[conversation_id]:
                try:
                    await connection.send_json(serializable_message)
                except WebSocketDisconnect:
                    # Mark for removal
                    disconnected.append(connection)
                    logger.warning(f"WebSocket disconnected during broadcast for conversation {conversation_id}")
                except Exception as e:
                    # Mark for removal but log the specific error
                    disconnected.append(connection)
                    logger.error(f"Error broadcasting message: {str(e)}", exc_info=True)

            # Clean up disconnected connections
            for connection in disconnected:
                if connection in self.active_connections[conversation_id]:
                    self.active_connections[conversation_id].remove(connection)

                    # Update connection status
                    if conversation_id in self.connection_status and connection in self.connection_status[conversation_id]:
                        self.connection_status[conversation_id][connection] = False
                        del self.connection_status[conversation_id][connection]

            # Clean up empty conversation entries
            if not self.active_connections[conversation_id]:
                del self.active_connections[conversation_id]
                if conversation_id in self.connection_status:
                    del self.connection_status[conversation_id]

    async def send_typing_indicator(self, conversation_id: str, is_typing: bool = True):
        """Send typing indicator to all clients connected to a conversation."""
        if conversation_id in self.active_connections:
            await self.broadcast({
                "type": "typing_indicator",
                "is_typing": is_typing,
                "conversation_id": conversation_id,
                "timestamp": datetime.now().isoformat()
            }, conversation_id)

    def get_connection_count(self, conversation_id: str) -> int:
        """Get the number of active connections for a conversation."""
        if conversation_id in self.active_connections:
            return len(self.active_connections[conversation_id])
        return 0


manager = ConnectionManager()


@router.websocket("/ws/{conversation_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    conversation_id: str,
    token: str = Query(...),
    db: Session = Depends(get_db)
):
    """
    WebSocket endpoint for real-time chat.

    Args:
        websocket: WebSocket connection
        conversation_id: ID of the conversation
        token: JWT token for authentication
    """
    # Log connection attempt
    logger.info(f"WebSocket connection attempt for conversation {conversation_id}")

    # Authenticate the user
    try:
        current_user = await get_current_user_from_token(token, db)
        logger.info(f"WebSocket authentication successful for user {current_user.id}")
    except Exception as e:
        logger.error(f"WebSocket authentication failed for conversation {conversation_id}: {str(e)}", exc_info=True)
        await websocket.close(code=1008, reason="Authentication failed")
        return

    # Get the conversation
    conversation = get_conversation(db, conversation_id)

    # Check if the conversation exists and belongs to the user
    if not conversation or conversation.user_id != current_user.id:
        logger.warning(f"WebSocket access denied: User {current_user.id} attempted to access conversation {conversation_id}")
        await websocket.close(code=1008, reason="Conversation not found or access denied")
        return

    # Connect to the WebSocket
    await manager.connect(websocket, conversation_id)

    try:
        while True:
            # Receive message from the client
            data = await websocket.receive_json()
            logger.debug(f"WebSocket received data: {data}")

            # Process the message
            if "message" in data:
                # Send typing indicator
                await manager.send_typing_indicator(conversation_id, True)

                # Create the user message
                user_message_id = str(uuid.uuid4())
                user_message_data = {
                    "id": user_message_id,
                    "conversation_id": conversation_id,
                    "sender": "user",
                    "content": data["message"],
                    "metadata": data.get("metadata", {}) # Original metadata
                }

                # Extract file attachments from message metadata
                message_attachments = user_message_data["metadata"].get("attachments", [])
                if message_attachments:
                    logger.info(f"Found {len(message_attachments)} file attachments in message metadata")

                # Extract temp_id from metadata if present
                temp_id = data.get("metadata", {}).pop("temp_id", None)
                logger.info(f"Received message with temp_id: {temp_id}")

                db_user_message = create_message(
                    db,
                    conversation_id=user_message_data["conversation_id"],
                    sender=user_message_data["sender"],
                    content=user_message_data["content"],
                    metadata=user_message_data.get("metadata", {})
                )
                logger.info(f"Created user message {user_message_id} in conversation {conversation_id}")

                # Assign the payload to the variable
                broadcast_payload = {
                    "type": "user_message",
                    "message": {
                        "id": db_user_message.id,
                        "conversation_id": db_user_message.conversation_id,
                        "sender": db_user_message.sender,
                        "content": db_user_message.content,
                        "metadata": db_user_message.message_metadata,
                        "created_at": db_user_message.created_at.isoformat(),
                        "temp_id": temp_id
                    }
                }
                logger.debug(f"Broadcasting user_message payload: {broadcast_payload}") # Log the payload
                await manager.broadcast(broadcast_payload, conversation_id)

                # Get the agent node from the factory
                agent_node = agent_factory.create_agent_node(conversation.persona_id)
                if not agent_node:
                    logger.error(f"No agent found for persona: {conversation.persona_id}")

                    # Send an error message
                    error_message_id = str(uuid.uuid4())
                    error_message_data = {
                        "id": error_message_id,
                        "conversation_id": conversation_id,
                        "sender": "ai",
                        "content": f"No agent found for persona: {conversation.persona_id}",
                        "metadata": {"error": "agent_not_found"}
                    }

                    db_error_message = create_message(
                        db,
                        conversation_id=error_message_data["conversation_id"],
                        sender=error_message_data["sender"],
                        content=error_message_data["content"],
                        metadata=error_message_data["metadata"]
                    )

                    # Turn off typing indicator
                    await manager.send_typing_indicator(conversation_id, False)

                    await manager.broadcast({
                        "type": "ai_message",
                        "message": {
                            "id": db_error_message.id,
                            "conversation_id": db_error_message.conversation_id,
                            "sender": db_error_message.sender,
                            "content": db_error_message.content,
                            "metadata": db_error_message.metadata,
                            "created_at": db_error_message.created_at.isoformat()
                        }
                    }, conversation_id)
                    continue

                # Call Orchestrator directly instead of using queue
                try:
                    # Prepare context
                    task_context = data.get("context", {})
                    metadata = data.get("metadata", {})

                    # Extract current_persona from context for proper routing
                    if "current_persona" in task_context:
                        logger.info(f"WebSocket: Found current_persona in context: {task_context['current_persona']}")

                    # Check for marketing_form_data in both metadata and context
                    if "marketing_form_data" in metadata:
                        task_context["marketing_form_data"] = metadata["marketing_form_data"]
                    elif "marketing_form_data" in task_context:
                        # Already in task_context from frontend, keep it
                        logger.info(f"Found marketing_form_data in context: {task_context['marketing_form_data']}")

                    # Also check for marketing_form_data directly in the context field
                    if "marketing_form_data" not in task_context and "marketing_form_data" in data.get("context", {}):
                        task_context["marketing_form_data"] = data["context"]["marketing_form_data"]
                        logger.info(f"Moved marketing_form_data from context to task_context: {task_context['marketing_form_data']}")
                    if "provider" in metadata:
                        task_context["provider"] = metadata["provider"]
                    if "model" in metadata:
                        task_context["model"] = metadata["model"]
                    if "data_source" in metadata:
                        task_context["data_source"] = metadata["data_source"]
                        logger.info(f"Found data_source in metadata: {metadata['data_source']}")

                    # Handle message-level file attachments first (highest priority)
                    if message_attachments and len(message_attachments) > 0:
                        logger.info(f"Processing {len(message_attachments)} message-level file attachments")

                        # Convert message attachments to data_source format
                        message_data_sources = []
                        for attachment in message_attachments:
                            data_source = {
                                "id": attachment.get("id"),
                                "name": attachment.get("name"),
                                "type": "file",
                                "file_path": attachment.get("file_path"),
                                "file_size": attachment.get("size"),
                                "num_rows": attachment.get("num_rows"),
                                "columns": attachment.get("columns"),
                                "source": "message_attachment",
                                "uploaded_at": attachment.get("uploaded_at")
                            }
                            message_data_sources.append(data_source)

                        # Set primary data source and attached files
                        task_context["data_source"] = message_data_sources[0]
                        task_context["attached_files"] = message_data_sources
                        task_context["message_attachments"] = message_attachments
                        logger.info(f"Set message-level attachments as primary data sources")

                    # Handle data_sources array from context (file attachments) - fallback
                    elif "data_sources" in task_context and "data_source" not in task_context:
                        data_sources = task_context["data_sources"]
                        if isinstance(data_sources, list) and len(data_sources) > 0:
                            # Use the first data source for single-file operations
                            # For multi-file support, agents can access the full array
                            primary_data_source = data_sources[0]
                            task_context["data_source"] = primary_data_source
                            logger.info(f"Using primary data_source from data_sources array: {primary_data_source}")

                            # Also preserve the full array for agents that support multiple files
                            task_context["attached_files"] = data_sources
                            logger.info(f"Preserved {len(data_sources)} data sources in attached_files")

                    # Handle file input attachments (convert attachment to data_source format)
                    if "attachment" in metadata and "data_source" not in task_context:
                        attachment = metadata["attachment"]
                        # Convert attachment to data_source format
                        task_context["data_source"] = {
                            "id": f"attachment_{attachment.get('name', 'unknown')}",
                            "name": attachment.get("name", "attached_file"),
                            "type": "file",
                            "attachment_info": attachment  # Keep original attachment info
                        }
                        logger.info(f"Converted attachment to data_source: {task_context['data_source']}")

                    logger.info(f"Final task_context: {task_context}")

                    # Create initial AI message placeholder
                    # Note: The actual message ID will be generated by create_message function

                    # Create initial metadata and sanitize it
                    initial_metadata = {
                        "status": "processing",
                        "processing": True,
                        "streaming": False, # Set to False as we process synchronously now
                        "complete": False
                    }

                    # Sanitize metadata to ensure it's JSON serializable
                    sanitized_initial_metadata = sanitize_json(initial_metadata)

                    initial_ai_message_data = {
                        # "id": ai_message_id, # Removed: ID is likely generated by the DB/create_message function
                        "conversation_id": conversation_id,
                        "sender": "ai",
                        "content": "Processing your request...",  # Initial message
                        "metadata": sanitized_initial_metadata
                    }
                    db_ai_message = create_message(db, **initial_ai_message_data)
                    logger.info(f"Created initial AI message {db_ai_message.id}")

                    # Send the initial message to indicate processing
                    await manager.broadcast({
                        "type": "ai_message_start", # Keep type for consistency? Or change?
                        "message": {
                            "id": db_ai_message.id,
                            "conversation_id": db_ai_message.conversation_id,
                            "sender": db_ai_message.sender,
                            "content": db_ai_message.content,
                            "metadata": db_ai_message.message_metadata,
                            "created_at": db_ai_message.created_at.isoformat()
                        }
                    }, conversation_id)

                    # Enhance context with conversation history for better context preservation
                    enhanced_context = task_context.copy() if task_context else {}

                    # Add user and conversation identifiers to context
                    enhanced_context["user_id"] = current_user.id
                    enhanced_context["conversation_id"] = conversation_id

                    # Ensure current_persona is preserved for routing
                    if "current_persona" in task_context:
                        enhanced_context["current_persona"] = task_context["current_persona"]
                        logger.info(f"🎯 WebSocket: Preserved current_persona for routing: {task_context['current_persona']}")
                    else:
                        # If no current_persona in context, try to derive from conversation
                        enhanced_context["current_persona"] = conversation.persona_id
                        logger.info(f"🎯 WebSocket: Using conversation persona_id as current_persona: {conversation.persona_id}")

                    # Final verification
                    final_persona = enhanced_context.get("current_persona")
                    logger.info(f"🎯 WebSocket: Final current_persona for orchestrator: {final_persona}")

                    # Ensure we never have None as current_persona
                    if not final_persona:
                        logger.error(f"❌ WebSocket: current_persona is None! This will cause routing issues.")
                        logger.error(f"❌ WebSocket: conversation.persona_id = {conversation.persona_id}")
                        logger.error(f"❌ WebSocket: task_context keys = {list(task_context.keys())}")
                        # Set a fallback to prevent system failure
                        enhanced_context["current_persona"] = conversation.persona_id or "concierge-agent"
                        logger.info(f"🔧 WebSocket: Set fallback current_persona: {enhanced_context['current_persona']}")

                    # Set routing information directly in context for streaming path
                    enhanced_context["selected_agent"] = final_persona
                    enhanced_context["conversation_mode"] = "conversation"
                    enhanced_context["agent_context"] = {"persona": final_persona, "context": "websocket_message"}
                    logger.info(f"🎯 WebSocket: Set routing context for {final_persona}")

                    # Get recent conversation history to preserve file context
                    try:
                        recent_messages = get_conversation_messages(db, conversation_id, limit=10)
                        conversation_history = []
                        latest_data_source = None

                        for msg in recent_messages:
                            message_data = {
                                "sender": msg.sender,
                                "content": msg.content,
                                "metadata": msg.message_metadata or {}
                            }
                            conversation_history.append(message_data)

                            # Extract data source context from message metadata
                            if msg.message_metadata and "data_source" in msg.message_metadata:
                                latest_data_source = msg.message_metadata["data_source"]

                        enhanced_context["conversation_history"] = conversation_history

                        # Preserve data source context if found and not already present
                        if latest_data_source and "data_source" not in enhanced_context:
                            enhanced_context["data_source"] = latest_data_source
                            logger.info(f"WebSocket: Preserved data source context from conversation history: {latest_data_source}")

                    except Exception as e:
                        logger.error(f"WebSocket: Error retrieving conversation history for context: {str(e)}")
                        # Continue without conversation history if there's an error

                    # Check if streaming is supported and enabled
                    supports_streaming = enhanced_context.get("enable_streaming", True)
                    logger.debug(f"🔍 WebSocket: Streaming enabled: {supports_streaming}")

                    if supports_streaming:
                        logger.debug(f"🚀 WebSocket: Starting streaming response for message {db_ai_message.id}")
                        # Handle streaming response
                        await handle_streaming_response(
                            db=db,
                            workflow_manager=workflow_manager,
                            user_id=current_user.id,
                            conversation_id=conversation_id,
                            message=data["message"],
                            context=enhanced_context,
                            ai_message_id=db_ai_message.id,  # Use the actual message ID from the database
                            manager=manager
                        )
                        logger.info(f"✅ WebSocket: Streaming response completed for message {db_ai_message.id}")

                        # Also preserve in user message metadata if context was provided
                        if task_context and "data_source" in task_context:
                            try:
                                user_message_metadata = {"data_source": task_context["data_source"]}
                                # Update the user message with data source metadata
                                update_message(db, db_user_message.id, metadata=user_message_metadata)
                                logger.info(f"WebSocket: Updated user message metadata with data source: {task_context['data_source']}")
                            except Exception as e:
                                logger.error(f"WebSocket: Error updating user message metadata: {str(e)}")

                        # Update conversation timestamp
                        update_conversation(db, conversation_id)

                    else:
                        # Handle non-streaming response using LangGraph
                        # Create unified state for workflow
                        initial_state = create_unified_state(
                            user_id=str(current_user.id),
                            conversation_id=conversation_id,
                            workflow_type="chat_message",
                            initial_message={
                                "content": data["message"],
                                "type": "user",
                                "timestamp": datetime.now().isoformat()
                            }
                        )

                        # Add enhanced context to state
                        initial_state.update(enhanced_context)

                        # Execute workflow using LangGraph
                        workflow_result = await workflow_manager.execute_workflow(
                            state=initial_state,
                            workflow_type="chat_message"
                        )

                        # Turn off typing indicator
                        await manager.send_typing_indicator(conversation_id, False)

                        # Extract response from workflow result
                        final_content = "No response generated."
                        agent_metadata = {}
                        if workflow_result and "result" in workflow_result:
                            result_data = workflow_result["result"]
                            if "messages" in result_data:
                                # Get the last agent message
                                agent_messages = [msg for msg in result_data["messages"] if msg.get("type") == "agent"]
                                if agent_messages:
                                    last_message = agent_messages[-1]
                                    final_content = last_message.get("content", final_content)
                                    # Extract metadata from agent message
                                    agent_metadata = last_message.get("metadata", {})
                                    logger.info(f"WebSocket: Extracted agent metadata keys: {list(agent_metadata.keys())}")

                        # Create base metadata and merge with agent metadata
                        final_metadata = {
                            "status": "completed",
                            "processing": False,
                            "streaming": False,
                            "complete": True,
                            "workflow_id": workflow_result.get("workflow_id") if workflow_result else None,
                            "execution_time": workflow_result.get("execution_time") if workflow_result else None
                        }

                        # Merge agent metadata (persona recommendations, etc.)
                        if agent_metadata:
                            final_metadata.update(agent_metadata)
                            logger.info(f"WebSocket: Merged agent metadata. Final keys: {list(final_metadata.keys())}")
                            if 'is_persona_recommendation' in final_metadata:
                                logger.info(f"WebSocket: Final metadata has is_persona_recommendation: {final_metadata['is_persona_recommendation']}")
                            if 'recommended_personas' in final_metadata:
                                logger.info(f"WebSocket: Final metadata has {len(final_metadata['recommended_personas'])} recommended personas")

                        # Preserve data source context in AI message metadata for future reference
                        if "data_source" in enhanced_context:
                            final_metadata["data_source"] = enhanced_context["data_source"]
                            logger.info(f"WebSocket: Preserved data source in AI message metadata: {enhanced_context['data_source']}")

                        # Also preserve in user message metadata if context was provided
                        if task_context and "data_source" in task_context:
                            try:
                                user_message_metadata = {"data_source": task_context["data_source"]}
                                # Update the user message with data source metadata
                                update_message(db, db_user_message.id, metadata=user_message_metadata)
                                logger.info(f"WebSocket: Updated user message metadata with data source: {task_context['data_source']}")
                            except Exception as e:
                                logger.error(f"WebSocket: Error updating user message metadata: {str(e)}")

                        # Sanitize metadata to ensure it's JSON serializable
                        sanitized_metadata = sanitize_json(final_metadata)

                        logger.info(f"Sanitized metadata for message {db_ai_message.id}")

                        updated_message = update_message(
                            db,
                            db_ai_message.id, # Use the ID from the created message
                            content=final_content,
                            metadata=sanitized_metadata
                        )

                        if updated_message:
                            logger.info(f"Updated AI message {updated_message.id} with orchestrator result")
                            # Broadcast the final message
                            await manager.broadcast({
                                "type": "ai_message_complete",
                                "message": {
                                    "id": updated_message.id,
                                    "conversation_id": updated_message.conversation_id,
                                    "sender": updated_message.sender,
                                    "content": updated_message.content,
                                    "metadata": updated_message.message_metadata,
                                    "created_at": updated_message.created_at.isoformat()
                                }
                            }, conversation_id)
                        else:
                             # Handle update failure (rare, but possible)
                             logger.error(f"Failed to update AI message {db_ai_message.id} after orchestrator call.")
                             # Optionally send an error message back
                             # Create error metadata and sanitize it
                             error_metadata = {
                                 "error": "update_failed",
                                 "status": "error",
                                 "processing": False,
                                 "streaming": False,
                                 "complete": True
                             }

                             # Sanitize metadata to ensure it's JSON serializable
                             sanitized_error_metadata = sanitize_json(error_metadata)
                             # Ensure db_ai_message is updated in the database if the initial update_message failed
                             db_ai_message.content = "Error updating final response."
                             db_ai_message.message_metadata = sanitized_error_metadata
                             try:
                                 db.commit()
                                 db.refresh(db_ai_message)
                                 logger.info(f"Successfully updated AI message {db_ai_message.id} with error via fallback in WebSocket.")
                             except Exception as commit_exc:
                                 logger.error(f"Fallback error update commit failed for AI message {db_ai_message.id} in WebSocket: {commit_exc}", exc_info=True)
                                 db.rollback()
                                 # If this also fails, the message in DB might be stale

                             await manager.broadcast({
                                 "type": "ai_message_error",
                                 "message": {
                                     "id": db_ai_message.id, # Use the ID of the message object we attempted to update
                                     "conversation_id": db_ai_message.conversation_id,
                                     "sender": db_ai_message.sender,
                                     "content": db_ai_message.content, # Reflect the content we tried to set
                                     "metadata": db_ai_message.message_metadata, # Reflect the metadata we tried to set
                                     "created_at": db_ai_message.created_at.isoformat()
                                 }
                             }, conversation_id)

                        # Update conversation timestamp
                        update_conversation(db, conversation_id)

                except Exception as e:
                    logger.error(f"Error processing message: {str(e)}", exc_info=True)

                    # Turn off typing indicator
                    await manager.send_typing_indicator(conversation_id, False)

                    # Use enhanced error handling if available
                    if ENHANCED_MARKETING_AVAILABLE and error_handler:
                        try:
                            error_context = {
                                'user_id': str(current_user.id),
                                'conversation_id': conversation_id,
                                'message': data["message"],
                                'enhanced_context': enhanced_context
                            }

                            error_response = error_handler.handle_exception(e, error_context)
                            error_content = error_response.get("message", "An error occurred while processing your message.")
                            error_metadata = error_response.get("metadata", {})

                            # Track error event
                            analytics = get_analytics()
                            if analytics:
                                analytics.track_error(
                                    error=e,
                                    user_id=str(current_user.id),
                                    conversation_id=conversation_id,
                                    context={'message': data["message"]}
                                )

                            logger.info("Used enhanced error handling for WebSocket error")

                        except Exception as error_handling_error:
                            logger.error(f"Enhanced error handling failed in WebSocket: {error_handling_error}")
                            # Fallback to basic error handling
                            error_content = "An error occurred while processing your message. Please try again later."
                            error_metadata = {
                                "error": str(e),
                                "streaming": False,
                                "complete": True,
                                "error_type": e.__class__.__name__
                            }
                    else:
                        # Basic error handling
                        error_content = "An error occurred while processing your message. Please try again later."
                        error_metadata = {
                            "error": str(e),
                            "streaming": False,
                            "complete": True,
                            "error_type": e.__class__.__name__
                        }

                    # Create an error message from the AI
                    error_message_id = str(uuid.uuid4())

                    # Sanitize metadata to ensure it's JSON serializable
                    sanitized_error_metadata = sanitize_json(error_metadata)

                    error_message_data = {
                        "id": error_message_id,
                        "conversation_id": conversation_id,
                        "sender": "ai",
                        "content": error_content,
                        "metadata": sanitized_error_metadata
                    }

                    db_error_message = create_message(
                        db,
                        conversation_id=error_message_data["conversation_id"],
                        sender=error_message_data["sender"],
                        content=error_message_data["content"],
                        metadata=error_message_data.get("metadata", {})
                    )

                    # Send the error message to all connected clients
                    await manager.broadcast({
                        "type": "ai_message_error",
                        "message": {
                            "id": db_error_message.id,
                            "conversation_id": db_error_message.conversation_id,
                            "sender": db_error_message.sender,
                            "content": db_error_message.content,
                            "metadata": db_error_message.message_metadata,
                            "created_at": db_error_message.created_at.isoformat()
                        }
                    }, conversation_id)

                    # Update the conversation's updated_at timestamp
                    # Just trigger the updated_at timestamp without changing any data
                    update_conversation(db, conversation_id)
            elif "ping" in data:
                # Handle ping messages to keep the connection alive
                await websocket.send_json({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                })
            elif "reconnect" in data:
                # Handle reconnection requests
                await manager.broadcast({
                    "type": "reconnect_confirmation",
                    "conversation_id": conversation_id,
                    "timestamp": datetime.now().isoformat()
                }, conversation_id)
            elif "type" in data and data["type"] == "refresh_conversation":
                # Handle refresh conversation requests
                try:
                    # Get the latest messages from the database
                    messages = get_conversation_messages(db, conversation_id)

                    # Send the messages to the client
                    await websocket.send_json({
                        "type": "conversation_refreshed",
                        "conversation_id": conversation_id,
                        "messages": [
                            {
                                "id": message.id,
                                "conversation_id": message.conversation_id,
                                "sender": message.sender,
                                "content": message.content,
                                "metadata": message.message_metadata,
                                "created_at": message.created_at.isoformat()
                            }
                            for message in messages
                        ],
                        "timestamp": datetime.now().isoformat()
                    })

                    logger.info(f"Refreshed conversation {conversation_id} for user {current_user.id}")
                except Exception as e:
                    logger.error(f"Error refreshing conversation: {str(e)}", exc_info=True)
                    await websocket.send_json({
                        "type": "error",
                        "message": f"Error refreshing conversation: {str(e)}",
                        "timestamp": datetime.now().isoformat()
                    })
    except WebSocketDisconnect as e:
        logger.info(f"WebSocket disconnected for conversation {conversation_id} with code {e.code}")
        manager.disconnect(websocket, conversation_id)
    except Exception as e:
        logger.error(f"WebSocket error for conversation {conversation_id}: {str(e)}", exc_info=True)
        try:
            # Try to send an error message to the client before disconnecting
            await websocket.send_json({
                "type": "error",
                "message": f"Server error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            })
        except Exception:
            # If sending the error message fails, just log it
            logger.error(f"Failed to send error message to WebSocket for conversation {conversation_id}")

        # Disconnect the WebSocket
        manager.disconnect(websocket, conversation_id)


# Message editing and resubmission endpoints

class EditMessageRequest(BaseModel):
    """Request model for editing a message."""
    message_id: str
    new_content: str
    metadata: Optional[Dict[str, Any]] = None

class EditMessageResponse(BaseModel):
    """Response model for editing a message."""
    original_message: MessageResponse
    edited_message: MessageResponse
    success: bool

@router.post("/messages/{message_id}/edit", response_model=EditMessageResponse)
async def edit_message(
    message_id: str,
    request: EditMessageRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Edit a message and create a new version.

    Args:
        message_id: ID of the message to edit
        request: Edit request with new content

    Returns:
        Original and edited message information
    """
    logger.info(f"User {current_user.id} editing message {message_id}")

    # Get the original message
    original_message = get_message(db, message_id)
    if not original_message:
        raise HTTPException(status_code=404, detail="Message not found")

    # Check if the message belongs to a conversation owned by the user
    conversation = get_conversation(db, original_message.conversation_id)
    if not conversation or conversation.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    try:
        # Create the edited message
        edited_message = create_message_edit(
            db=db,
            original_message_id=message_id,
            new_content=request.new_content,
            metadata=request.metadata
        )

        # Convert to response models
        original_response = MessageResponse(
            id=original_message.id,
            conversation_id=original_message.conversation_id,
            sender=original_message.sender,
            content=original_message.content,
            metadata=original_message.message_metadata,
            created_at=original_message.created_at
        )

        edited_response = MessageResponse(
            id=edited_message.id,
            conversation_id=edited_message.conversation_id,
            sender=edited_message.sender,
            content=edited_message.content,
            metadata=edited_message.message_metadata,
            created_at=edited_message.created_at
        )

        return EditMessageResponse(
            original_message=original_response,
            edited_message=edited_response,
            success=True
        )

    except Exception as e:
        logger.error(f"Error editing message {message_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to edit message")


class ResubmitMessageRequest(BaseModel):
    """Request model for resubmitting a message."""
    message_id: str
    context: Optional[Dict[str, Any]] = None

@router.post("/messages/{message_id}/resubmit", response_model=SendMessageResponse)
async def resubmit_message(
    message_id: str,
    request: ResubmitMessageRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Resubmit a message for reprocessing by the agent.

    Args:
        message_id: ID of the message to resubmit
        request: Resubmission request with optional context

    Returns:
        User message and new agent response
    """
    logger.info(f"User {current_user.id} resubmitting message {message_id}")

    # Get the message to resubmit
    message_to_resubmit = get_message(db, message_id)
    if not message_to_resubmit:
        raise HTTPException(status_code=404, detail="Message not found")

    # Check if the message belongs to a conversation owned by the user
    conversation = get_conversation(db, message_to_resubmit.conversation_id)
    if not conversation or conversation.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Only allow resubmitting user messages
    if message_to_resubmit.sender != "user":
        raise HTTPException(status_code=400, detail="Only user messages can be resubmitted")

    try:
        # Create a new send message request with the resubmitted content
        resubmit_request = SendMessageRequest(
            conversation_id=message_to_resubmit.conversation_id,
            message=message_to_resubmit.content,
            context=request.context,
            metadata={"resubmitted_from": message_id, "is_resubmission": True}
        )

        # Process the resubmitted message using the existing send_message logic
        return await send_message_to_agent(resubmit_request, db, current_user)

    except Exception as e:
        logger.error(f"Error resubmitting message {message_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to resubmit message")


@router.get("/messages/{message_id}/thread")
async def get_message_thread_endpoint(
    message_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get the thread of messages for a given message.

    Args:
        message_id: ID of the message to get thread for

    Returns:
        List of messages in the thread
    """
    # Get the message
    message = get_message(db, message_id)
    if not message:
        raise HTTPException(status_code=404, detail="Message not found")

    # Check access
    conversation = get_conversation(db, message.conversation_id)
    if not conversation or conversation.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Get the thread
    thread_id = message.thread_id or message.id
    thread_messages = get_message_thread(db, thread_id)

    # Convert to response format
    response_messages = []
    for msg in thread_messages:
        response_messages.append(MessageResponse(
            id=msg.id,
            conversation_id=msg.conversation_id,
            sender=msg.sender,
            content=msg.content,
            metadata=msg.message_metadata,
            created_at=msg.created_at
        ))

    return {"thread_id": thread_id, "messages": response_messages}


@router.get("/messages/{message_id}/edit-history")
async def get_message_edit_history_endpoint(
    message_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get the edit history for a message.

    Args:
        message_id: ID of the original message

    Returns:
        List of edited versions of the message
    """
    # Get the original message
    original_message = get_message(db, message_id)
    if not original_message:
        raise HTTPException(status_code=404, detail="Message not found")

    # Check access
    conversation = get_conversation(db, original_message.conversation_id)
    if not conversation or conversation.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Get edit history
    edit_history = get_message_edit_history(db, message_id)

    # Convert to response format
    response_edits = []
    for edit in edit_history:
        response_edits.append(MessageResponse(
            id=edit.id,
            conversation_id=edit.conversation_id,
            sender=edit.sender,
            content=edit.content,
            metadata=edit.message_metadata,
            created_at=edit.created_at
        ))

    return {"original_message_id": message_id, "edits": response_edits}
